<?php

/**
 * Template Name: Home Page Template
 */
get_header();
global $post;
$meta = get_field_objects($post->ID);
$home_meta = get_field_objects(9);
?>

<div id="home"></div>
<div class="container-fluid main-banner-container gen-rel">
    <div class="row">
        <div class="col-md-12 noleftpadding norightpadding main-video gen-rel">
            <video autoplay="" muted="" loop="" poster="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/poster.jpg" id="myVideo" class="banner-video">
                <source src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/video1.mp4" type="video/mp4">
            </video>

            <div class="main-video-inner-left">
                <div class="main-video-inner-left2">
                    <h1>Quantum Property <br>Advisors</h1>
                    <h2>SPECIALISTS IN SELF STORAGE and HOSPITALITY REAL ESTATE</h2>
                    <!--                     <a href="#myModal22" data-toggle="modal">COVID-19 & Self Storage Industry</a> -->
                </div>
            </div>

            <div class="main-video-inner-right">
                <div class="main-video-inner-right2">
                    <h2>What We Do | Our Services</h2>
                    <div class="main-video-inner-right2-sec">
                        <div class="main-video-inner-right2-sec1">
                            <img class="mysvg" src="<?php echo $home_meta['service_icon_1']['value'] ?>">
                            <h3><?php echo $home_meta['service_heading_1']['value'] ?></h3>
                            <p><?php echo $home_meta['service_content_1']['value'] ?></p>
                        </div>
                        <div class="main-video-inner-right2-sec2">
                            <img class="mysvg" src="<?php echo $home_meta['service_icon_2']['value'] ?>">
                            <h3><?php echo $home_meta['service_heading_2']['value'] ?></h3>
                            <p><?php echo $home_meta['service_content_2']['value'] ?></p>
                        </div>
                        <div class="main-video-inner-right2-sec3">
                            <img class="mysvg" src="<?php echo $home_meta['service_icon_3']['value'] ?>">
                            <h3><?php echo $home_meta['service_heading_3']['value'] ?></h3>
                            <p><?php echo $home_meta['service_content_3']['value'] ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid bg-green showOnMobile">
    <div class="container">
        <div class="row marginmd">
            <div class="col-md-12">
                <h2>What We Do | Our Services</h2>
                <div class="">
                    <div class="showOnMobileInner">
                        <img class="mysvg" src="<?php echo $home_meta['service_icon_1']['value'] ?>">
                        <h3><?php echo $home_meta['service_heading_1']['value'] ?></h3>
                        <p><?php echo $home_meta['service_content_1']['value'] ?></p>
                    </div>
                    <div class="showOnMobileInner">
                        <img class="mysvg" src="<?php echo $home_meta['service_icon_2']['value'] ?>">
                        <h3><?php echo $home_meta['service_heading_2']['value'] ?></h3>
                        <p><?php echo $home_meta['service_content_2']['value'] ?></p>
                    </div>
                    <div class="showOnMobileInner">
                        <img class="mysvg" src="<?php echo $home_meta['service_icon_3']['value'] ?>">
                        <h3><?php echo $home_meta['service_heading_3']['value'] ?></h3>
                        <p><?php echo $home_meta['service_content_3']['value'] ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="about"></div>
<div class="container-fluid">
    <div class="container resp-full">
        <div class="row marginmd nobottommargin about-quantum-title centered">
            <div class="col-md-12">
                <div class="sec-title-two text-center">
                    <h2>About </h2>
                    <h3>Quantum Property Advisors</h3>
                </div>
            </div>
        </div>

        <!--<div class="row marginsm">
                <div class="col-md-12 noleftpadding norightpadding">
                    <div class="quantumpa-profile">
                        <a class="open-button" onclick="openForm()"><i class="fa fa-chevron-right"></i></a>
                        <div class="quantumpa-sec" id="quantumpa-sec">
                            <a class="btn-close" onclick="closeForm()"><i class="fas fa-times"></i></a>
                            <div class="row">
                                <div class="col-md-4 col-sm-4 col-xs-12 img-100">
                                    <img src="<?php /*echo esc_url(get_template_directory_uri()); */ ?>/assets/img/norman.jpg" alt="">
                                </div>
                                <div class="col-md-8 col-sm-8 col-xs-12">
                                    <div class="quantumpa-inner-sec">
                                        <h2>Norman Herd</h2>
                                        <span>Designated Broker</span>
                                        <a href="tel:6025625500">************</a>
                                        <a href="mailto:<EMAIL>"><EMAIL></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="quantumpa-text">
                            <?php /*echo $home_meta['about_content']['value'] */ ?>
                        </div>
                    </div>
                </div>
            </div>-->

        <!--         <div class="row marginsm">
            <div class="col-md-12 noleftpadding norightpadding" style="overflow: hidden">
                <div class="quantumpa-profile">
                    <a class="open-button" onclick="openForm()"><i class="fa fa-chevron-right"></i></a>
                    <div class="quantumpa-sec" id="quantumpa-sec">
                        <a class="btn-close" onclick="closeForm()"><i class="fas fa-times"></i></a>
                        <div class="row">
                            <div class="col-md-12 img-100">
                                <img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/norman.jpg" alt="">
                                <div class="quantumpa-inner-sec">
                                    <h2>Norman Herd <a href="#"><i class="fab fa-linkedin"></i></a></h2>
                                    <span>Designated Broker</span> <br>
                                    <a href="tel:6025625500" class="phone"><i class="fa fa-phone-square"></i> ************</a>
                                    <a href="mailto:<EMAIL>" class="email"><i class="fa fa-envelope"></i> <EMAIL></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="quantumpa-text">
                        <?php echo $home_meta['about_content']['value'] ?>
                    </div>
                </div>
            </div>
        </div> -->


        <div class="row marginsm">
            <div class="col-md-12 noleftpadding norightpadding" style="overflow: hidden">
                <div class="quantumpa-profile">
                    <div class="quantumpa-sec" id="quantumpa-sec">
                        <div class="row">
                            <div class="col-md-12 img-100">
                                <img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/norman.jpg" alt="">
                                <div class="quantumpa-inner-sec">
                                    <h2>Norman Herd <a href="#"><i class="fab fa-linkedin"></i></a></h2>
                                    <span>Designated Broker</span> <br>
                                    <a href="tel:6027705080" class="phone"><i class="fa fa-phone-square"></i> (*************</a>
                                    <a href="mailto:<EMAIL>" class="email"><i class="fa fa-envelope"></i> <EMAIL></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="quantumpa-text">
                        <?php echo $home_meta['about_content']['value'] ?>
                    </div>
                </div>
            </div>
        </div>






    </div>
</div>

<div class="line"></div>
<div class="container-fluid">
    <div class="" style="overflow: hidden;">
        <div class="row marginsm">
            <div class="swiper3">
                <div class="swiper-wrapper">
                    <?php
                    $args = query_posts(
                        array(
                            'post_type'      => 'extra_space', // This is the name of your CPT
                            'order'          => 'ASC',
                            'posts_per_page' => -1
                        )
                    );
                    if (have_posts()) {
                        while (have_posts()) : the_post();
                            $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
                    ?>
                            <div class="swiper-slide">
                                <div class="box">
                                    <img src="<?php echo $image[0]; ?>" alt="">
                                    <div class="box-layer layer-1"></div>
                                    <div class="box-layer layer-2"></div>
                                    <div class="box-layer layer-3"></div>
                                </div>
                            </div>

                    <?php
                        endwhile;
                    }
                    ?>
                </div>
                <!-- Add Arrows -->
                <!--<div class="swiper-button-next6"></div>
                <div class="swiper-button-prev6"></div>-->
            </div>
        </div>
    </div>
</div>
<div class="line"></div>

<div id="clients"></div>
<div class="container-fluid">
    <div class="container">
        <div class="row marginmd nobottommargin centered">
            <div class="col-md-12">
                <div class="sec-title-two text-center">
                    <h2>Clients Served</h2>
                    <h4>We’ve had the Pleasure of Working with the best</h4>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container resp-full">
    <div class="row marginsm">
        <?php $ids = get_post_meta(9, 'vdw_gallery_id', true); ?>
        <!-- Swiper -->
        <div class="swiper-container swiper2">
            <div class="swiper-wrapper">
                <?php
                if ($ids) {
                    foreach ($ids as $key => $value) {
                        $image = wp_get_attachment_image_src($value, 'medium');
                ?>
                        <div class="swiper-slide">
                            <ul class="my-gallery img-100">
                                <li class="client-logo">
                                    <img class="image-preview" src="<?php echo $image[0]; ?>">
                                </li>
                            </ul>
                        </div>
                <?php
                    }
                }
                ?>
            </div>
        </div>
    </div>
</div>


<div id="experience"></div>
<div class="container-fluid bg-light-gray">
    <div class="container resp-full">
        <div class="row marginmd nobottommargin centered">
            <div class="col-md-12">
                <div class="sec-title-two text-center">
                    <h2>Self Storage Experience</h2>
                </div>
            </div>
        </div>
        <div class="row marginsm">
            <?php
            $args = query_posts(array(
                'post_type' => 'experience', // This is the name of your CPT
                'tax_query' => array(
                    array(
                        'taxonomy' => 'category', // This is the name of your taxonomy
                        'field' => 'slug',
                        'terms' => array('storage-experience'), // This is your taxonomy term
                    )
                ),
                'order' => 'ASC',
                'posts_per_page' => -1
            ));
            $counter = 0;
            if (have_posts()) {
                while (have_posts()) : the_post();
                    $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
            ?>
                    <div class="seCol-<?php echo $counter + 1; ?> gen-rel">
                        <div class="storageExperienceBox" style="background: url(<?php echo $image[0]; ?>) center center no-repeat; background-size: cover;">
                            <div class="storageExperienceInfo">
                                <div class="storageExperienceInfoLeft">
                                    <h1><?php the_title(); ?></h1>
                                    <h4><?php the_field('square_ft'); ?></h4>
                                </div>
                                <div class="storageExperienceInfoRight">
                                    <h4><?php the_field('address'); ?></h4>
                                    <h4><?php the_field('citystate'); ?></h4>
                                </div>
                            </div>
                            <div class="storageExperienceInnerBox" style="background: url(<?php echo $image[0]; ?>) center center no-repeat; background-size: cover;">
                                <div class="storageExperienceInnerInfo">
                                    <div class="storageExperienceInnerInfoLeft">
                                        <h1><?php the_title(); ?></h1>
                                        <h4><?php the_field('square_ft'); ?></h4>
                                    </div>
                                    <div class="storageExperienceInnerInfoRight">
                                        <h4><?php the_field('address'); ?></h4>
                                        <h4><?php the_field('citystate'); ?></h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
            <?php
                    $counter++;
                endwhile;
            }
            wp_reset_query();
            ?>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="container resp-full">
        <div class="row marginmd nobottommargin centered">
            <div class="col-md-12">
                <div class="sec-title-two text-center">
                    <h2>Hospitality Experience</h2>
                </div>
            </div>
        </div>
        <div class="row marginsm">
            <?php
            $args = query_posts(array(
                'post_type' => 'experience', // This is the name of your CPT
                'tax_query' => array(
                    array(
                        'taxonomy' => 'category', // This is the name of your taxonomy
                        'field' => 'slug',
                        'terms' => array('hospitality-experience'), // This is your taxonomy term
                    )
                ),
                'order' => 'ASC',
                'posts_per_page' => -1
            ));
            $counter = 0;
            if (have_posts()) {
                while (have_posts()) : the_post();
                    $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
            ?>
                    <div class="heCol-<?php echo $counter + 1; ?> gen-rel">
                        <div class="hospitalityExperienceBox" style="background: url(<?php echo $image[0]; ?>) center center no-repeat; background-size: cover;">
                            <div class="hospitalityExperienceInfo">
                                <div class="hospitalityExperienceInfoLeft">
                                    <h1><?php the_title(); ?></h1>
                                    <h4><?php the_field('square_ft'); ?></h4>
                                </div>
                                <div class="hospitalityExperienceInfoRight">
                                    <h4><?php the_field('address'); ?></h4>
                                    <h4><?php the_field('citystate'); ?></h4>
                                </div>
                            </div>
                            <div class="hospitalityExperienceInnerBox" style="background: url(<?php echo $image[0]; ?>) center center no-repeat; background-size: cover;">
                                <div class="hospitalityExperienceInnerInfo">
                                    <div class="hospitalityExperienceInnerInfoLeft">
                                        <h1><?php the_title(); ?></h1>
                                        <h4><?php the_field('square_ft'); ?></h4>
                                    </div>
                                    <div class="hospitalityExperienceInnerInfoRight">
                                        <h4><?php the_field('address'); ?></h4>
                                        <h4><?php the_field('citystate'); ?></h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
            <?php
                    $counter++;
                endwhile;
            }
            wp_reset_query();
            ?>
        </div>
    </div>
</div>

<div class="container-fluid bg-light-gray">
    <div class="container resp-full">
        <div class="row marginmd nobottommargin centered">
            <div class="col-md-12">
                <div class="sec-title-two text-center">
                    <h2>Other Experience</h2>
                </div>
            </div>
        </div>
        <div class="row marginsm">
            <?php
            $args = query_posts(array(
                'post_type' => 'experience', // This is the name of your CPT
                'tax_query' => array(
                    array(
                        'taxonomy' => 'category', // This is the name of your taxonomy
                        'field' => 'slug',
                        'terms' => array('single-use-developments'), // This is your taxonomy term
                    )
                ),
                'order' => 'ASC',
                'posts_per_page' => -1
            ));
            $counter = 0;
            if (have_posts()) {
                while (have_posts()) : the_post();
                    $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
            ?>
                    <div class="sudCol-<?php echo $counter + 1; ?> gen-rel">
                        <div class="suDevelopmentBox" style="background: url(<?php echo $image[0]; ?>) center center no-repeat; background-size: cover;">
                            <div class="suDevelopmentInfo">
                                <div class="suDevelopmentInfoLeft">
                                    <h1><?php the_title(); ?></h1>
                                    <h4><?php the_field('square_ft'); ?></h4>
                                </div>
                                <div class="suDevelopmentInfoRight">
                                    <h4><?php the_field('address'); ?></h4>
                                    <h4><?php the_field('citystate'); ?></h4>
                                </div>
                            </div>
                            <div class="suDevelopmentInnerBox" style="background: url(<?php echo $image[0]; ?>) center center no-repeat; background-size: cover;">
                                <div class="suDevelopmentInnerInfo">
                                    <div class="suDevelopmentInnerInfoLeft">
                                        <h1><?php the_title(); ?></h1>
                                        <h4><?php the_field('square_ft'); ?></h4>
                                    </div>
                                    <div class="suDevelopmentInnerInfoRight">
                                        <h4><?php the_field('address'); ?></h4>
                                        <h4><?php the_field('citystate'); ?></h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
            <?php
                    $counter++;
                endwhile;
            }
            wp_reset_query();
            ?>
        </div>
    </div>
</div>


<div id="contactus"></div>
<div class="container-fluid sayHelloSection">
    <div class="container">
        <div class="row marginmd nobottommargin centered">
            <div class="col-md-12">
                <div class="sec-title-two-white text-center">
                    <h2>Request Information</h2>
                    <p>Please fill the form below if you want to consult one of our team members to learn more about a specific service</p>
                </div>
            </div>
        </div>

        <div class="row marginmd word-wrap wow bounceInRight">
            <?php echo do_shortcode('[contact-form-7 id="8" title="Contact Form"]'); ?>
        </div>
    </div>
</div>


<div class="covid-19">
    <div class="covid-19-inner">
        <a href="#myModal22" data-toggle="modal"> COVID-19 & Self Storage Industry</a>
    </div>
</div>

<div id="myModal22" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-content-area">
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">Back to Home</button>
        <div class="modal-body">
            <div class="container-fluid gen-rel main_banner" style="background: url('<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/storage-industry.jpg') center center repeat-x; background-size: cover">
                <div class="container main_banner_inner resp-full">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="main_banner_inner_text">
                                <?php echo $home_meta['covid-19_main_banner_text']['value']; ?>
                                <a href="<?php bloginfo('url'); ?>"><img src="<?php echo $home_meta['covid-19_main_banner_logo']['value']; ?>" alt=""></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="container-fluid covid-main-text">
                <div class="container resp-full">
                    <div class="row marginsm">
                        <div class="col-md-12 noleftpadding norightpadding" style="overflow: hidden">
                            <div class="quantumpa-modal">
                                <a class="open-button-modal" onclick="openFormModal()"><i class="fa fa-chevron-right"></i></a>
                                <div class="quantumpa-sec-modal" id="quantumpa-sec-modal">
                                    <a class="btn-close-modal" onclick="closeFormModal()"><i class="fas fa-times"></i></a>
                                    <div class="row">
                                        <div class="col-md-12 profile-picture">
                                            <img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/norman.jpg" alt="">
                                            <div class="quantumpa-inner-sec-modal">
                                                <h2>Norman Herd <a href="#"><i class="fab fa-linkedin"></i></a></h2>
                                                <span>Designated Broker</span> <br>
                                                <a href="tel:6025625500" class="phone"><i class="fa fa-phone-square"></i> ************</a>
                                                <a href="mailto:<EMAIL>" class="email"><i class="fa fa-envelope"></i> <EMAIL></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="quantumpa-text-modal">
                                    <?php echo $home_meta['covid-19_main_text']['value']; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="container-fluid">
                <div class="container resp-full">
                    <div class="row marginmd nobottommargin">
                        <div class="custom-column">
                            <a href="#" class="tablinks" onclick="openTab(event, 'operations')" id="defaultOpen">
                                <div class="column card">
                                    <h3>Operations</h3>
                                </div>
                            </a>
                        </div>

                        <div class="custom-column">
                            <a href="#" class="tablinks" onclick="openTab(event, 'employees')">
                                <div class="column card">
                                    <h3>Employees</h3>
                                </div>
                            </a>
                        </div>

                        <div class="custom-column">
                            <a href="#" class="tablinks" onclick="openTab(event, 'construction-finance-and-investment')">
                                <div class="column card">
                                    <h3>Real Estate, Finance and Investment</h3>
                                </div>
                            </a>
                        </div>

                        <div class="custom-column">
                            <a href="#" class="tablinks" onclick="openTab(event, 'what-other-operators-are-doing')">
                                <div class="column card">
                                    <h3>What other operators are doing...</h3>
                                </div>
                            </a>
                        </div>
                    </div>

                    <!--Show Operations Row Onclick Operation button & Active Operation Button-->
                    <div class="row marginsm tabcontent" id="operations">
                        <?php
                        $args = array(
                            'post_type' => 'covid19_resources',
                            'order' => 'ASC',
                            'tax_query' => array(
                                array(
                                    'taxonomy' => 'category', // This is the name of your taxonomy
                                    'field' => 'slug',
                                    'terms' => array('operations'), // This is your taxonomy term
                                )
                            ),
                            'posts_per_page' => -1
                        );
                        $loop = new WP_Query($args);
                        while ($loop->have_posts()) : $loop->the_post();
                            $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
                        ?>

                            <?php
                            $covid = get_field('covid-19_pdf');
                            if ($covid) {
                                foreach ($covid as $row) {
                            ?>
                                    <div class="col-md-2 col-sm-4 col-xs-6 col-xxs-12">
                                        <a href="<?php echo $row['covid-19_download_pdf']; ?>" target="_blank" class="covid-inner-box">
                                            <img src="<?php echo $row['covid-19_download_pdf_image']; ?>" alt="">
                                            <h5><?php echo $row['covid-19_download_pdf_title']; ?></h5>
                                        </a>
                                    </div>
                            <?php }
                            }
                            ?>

                            <?php
                            $covidArticle = get_field('covid-19_article');
                            if ($covidArticle) {
                                foreach ($covidArticle as $rowA) {
                            ?>
                                    <div class="col-md-2 col-sm-4 col-xs-6 col-xxs-12">
                                        <a href="<?php echo $rowA['covid-19_article_link']; ?>" target="_blank" class="covid-inner-box covid-article">
                                            <img src="<?php echo $rowA['covid-19_article_image']; ?>" alt="">
                                            <h5><?php echo $rowA['covid-19_article_title']; ?></h5>
                                        </a>
                                    </div>
                            <?php }
                            }
                            ?>
                        <?php
                        endwhile;
                        wp_reset_query();
                        ?>
                    </div>

                    <!--Show Employees Row Onclick Employees button & Active Employees Button-->
                    <div class="row marginsm tabcontent" id="employees">
                        <?php
                        $args = array(
                            'post_type' => 'covid19_resources',
                            'order' => 'ASC',
                            'tax_query' => array(
                                array(
                                    'taxonomy' => 'category', // This is the name of your taxonomy
                                    'field' => 'slug',
                                    'terms' => array('employees'), // This is your taxonomy term
                                )
                            ),
                            'posts_per_page' => -1
                        );
                        $loop = new WP_Query($args);
                        while ($loop->have_posts()) : $loop->the_post();
                            $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
                        ?>

                            <?php
                            $covid = get_field('covid-19_pdf');
                            if ($covid) {
                                foreach ($covid as $row) {
                            ?>
                                    <div class="col-md-2 col-sm-4 col-xs-6 col-xxs-12">
                                        <a href="<?php echo $row['covid-19_download_pdf']; ?>" target="_blank" class="covid-inner-box">
                                            <img src="<?php echo $row['covid-19_download_pdf_image']; ?>" alt="">
                                            <h5><?php echo $row['covid-19_download_pdf_title']; ?></h5>
                                        </a>
                                    </div>
                            <?php }
                            }
                            ?>

                            <?php
                            $covidArticle = get_field('covid-19_article');
                            if ($covidArticle) {
                                foreach ($covidArticle as $rowA) {
                            ?>
                                    <div class="col-md-2 col-sm-4 col-xs-6 col-xxs-12">
                                        <a href="<?php echo $rowA['covid-19_article_link']; ?>" target="_blank" class="covid-inner-box covid-article">
                                            <img src="<?php echo $rowA['covid-19_article_image']; ?>" alt="">
                                            <h5><?php echo $rowA['covid-19_article_title']; ?></h5>
                                        </a>
                                    </div>
                            <?php }
                            }
                            ?>

                        <?php
                        endwhile;
                        wp_reset_query();
                        ?>
                    </div>

                    <!--Show Construction Finance & Investment Row Onclick Construction Finance & Investment button & Active Construction Finance & Investment Button-->
                    <div class="row marginsm tabcontent" id="construction-finance-and-investment">
                        <?php
                        $args = array(
                            'post_type' => 'covid19_resources',
                            'order' => 'ASC',
                            'tax_query' => array(
                                array(
                                    'taxonomy' => 'category', // This is the name of your taxonomy
                                    'field' => 'slug',
                                    'terms' => array('construction-finance-and-investment'), // This is your taxonomy term
                                )
                            ),
                            'posts_per_page' => -1
                        );
                        $loop = new WP_Query($args);
                        while ($loop->have_posts()) : $loop->the_post();
                            $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
                        ?>

                            <?php
                            $covid = get_field('covid-19_pdf');
                            if ($covid) {
                                foreach ($covid as $row) {
                            ?>
                                    <div class="col-md-2 col-sm-4 col-xs-6 col-xxs-12">
                                        <a href="<?php echo $row['covid-19_download_pdf']; ?>" target="_blank" class="covid-inner-box">
                                            <img src="<?php echo $row['covid-19_download_pdf_image']; ?>" alt="">
                                            <h5><?php echo $row['covid-19_download_pdf_title']; ?></h5>
                                        </a>
                                    </div>
                            <?php }
                            }
                            ?>

                            <?php
                            $covidArticle = get_field('covid-19_article');
                            if ($covidArticle) {
                                foreach ($covidArticle as $rowA) {
                            ?>
                                    <div class="col-md-2 col-sm-4 col-xs-6 col-xxs-12">
                                        <a href="<?php echo $rowA['covid-19_article_link']; ?>" target="_blank" class="covid-inner-box covid-article">
                                            <img src="<?php echo $rowA['covid-19_article_image']; ?>" alt="">
                                            <h5><?php echo $rowA['covid-19_article_title']; ?></h5>
                                        </a>
                                    </div>
                            <?php }
                            }
                            ?>
                        <?php
                        endwhile;
                        wp_reset_query();
                        ?>
                    </div>

                    <!--Show What Other Operators are doing Row Onclick What Other Operators are doing button & Active What Other Operators are doing Button-->
                    <div class="row marginsm tabcontent" id="what-other-operators-are-doing">
                        <?php
                        $args = array(
                            'post_type' => 'covid19_resources',
                            'order' => 'ASC',
                            'tax_query' => array(
                                array(
                                    'taxonomy' => 'category', // This is the name of your taxonomy
                                    'field' => 'slug',
                                    'terms' => array('what-other-operators-are-doing'), // This is your taxonomy term
                                )
                            ),
                            'posts_per_page' => -1
                        );
                        $loop = new WP_Query($args);
                        while ($loop->have_posts()) : $loop->the_post();
                            $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
                        ?>

                            <?php
                            $covid = get_field('covid-19_pdf');
                            if ($covid) {
                                foreach ($covid as $row) {
                            ?>
                                    <div class="col-md-2 col-sm-4 col-xs-6 col-xxs-12">
                                        <a href="<?php echo $row['covid-19_download_pdf']; ?>" target="_blank" class="covid-inner-box">
                                            <img src="<?php echo $row['covid-19_download_pdf_image']; ?>" alt="">
                                            <h5><?php echo $row['covid-19_download_pdf_title']; ?></h5>
                                        </a>
                                    </div>
                            <?php }
                            }
                            ?>

                            <?php
                            $covidArticle = get_field('covid-19_article');
                            if ($covidArticle) {
                                foreach ($covidArticle as $rowA) {
                            ?>
                                    <div class="col-md-2 col-sm-4 col-xs-6 col-xxs-12">
                                        <a href="<?php echo $rowA['covid-19_article_link']; ?>" target="_blank" class="covid-inner-box covid-article">
                                            <img src="<?php echo $rowA['covid-19_article_image']; ?>" alt="">
                                            <h5><?php echo $rowA['covid-19_article_title']; ?></h5>
                                        </a>
                                    </div>
                            <?php }
                            }
                            ?>
                        <?php
                        endwhile;
                        wp_reset_query();
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php get_footer(); ?>